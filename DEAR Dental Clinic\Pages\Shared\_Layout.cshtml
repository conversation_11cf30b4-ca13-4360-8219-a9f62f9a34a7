﻿<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>@ViewData["Title"] - DEAR_Dental_Clinic</title>
    <script type="importmap"></script>
    <!-- MDB5 CSS -->
    <link rel="stylesheet" href="~/lib/MDB5-STANDARD-UI-KIT/css/mdb.min.css" />
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" />
    <!-- Custom CSS -->
    <link rel="stylesheet" href="~/css/site.css" asp-append-version="true" />
    <link rel="stylesheet" href="~/DEAR_Dental_Clinic.styles.css" asp-append-version="true" />
</head>
<body>
    <header>
        <!-- MDB5 Responsive Navbar -->
        <nav class="navbar navbar-expand-xl navbar-light bg-white shadow-1">
            <div class="container">
                <a class="navbar-brand fw-bold" asp-area="" asp-page="/Index">
                    <i class="fas fa-tooth text-primary me-2"></i>
                    <span class="d-none d-sm-inline">DEAR Dental Clinic</span>
                    <span class="d-inline d-sm-none">DEAR</span>
                </a>
                <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav"
                        aria-controls="navbarNav" aria-expanded="false" aria-label="Toggle navigation">
                    <i class="fas fa-bars"></i>
                </button>
                <div class="collapse navbar-collapse" id="navbarNav">
                    <ul class="navbar-nav mx-auto">
                        <li class="nav-item">
                            <a class="nav-link fw-bold @(ViewContext.RouteData.Values["page"]?.ToString() == "/Index" ? "active text-primary" : "")" asp-area="" asp-page="/Index">Home</a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link fw-bold @(ViewContext.RouteData.Values["page"]?.ToString() == "/Services" ? "active text-primary" : "")" asp-area="" asp-page="/Services">Services</a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link fw-bold @(ViewContext.RouteData.Values["page"]?.ToString() == "/About" ? "active text-primary" : "")" asp-area="" asp-page="/About">About</a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link fw-bold @(ViewContext.RouteData.Values["page"]?.ToString() == "/Contact" ? "active text-primary" : "")" asp-area="" asp-page="/Contact">Contact</a>
                        </li>
                    </ul>
                    <div class="d-flex">
                        <a class="btn btn-outline-primary rounded-pill px-4" asp-area="" asp-page="/Login">
                            <i class="fas fa-sign-in-alt me-2"></i>Login
                        </a>
                    </div>
                </div>
            </div>
        </nav>
    </header>
    <main role="main">
        @RenderBody()
    </main>

    <!-- MDB5 Footer -->
    <footer class="bg-primary text-white py-4">
        <div class="container">
            <div class="row">
                <div class="col-md-6">
                    <h5 class="fw-bold"><i class="fas fa-tooth me-2"></i>DEAR Dental Clinic</h5>
                    <p class="mb-0">Your trusted partner for comprehensive dental care.</p>
                </div>
                <div class="col-md-6 text-md-end">
                    <p class="mb-1">&copy; @DateTime.Now.Year DEAR Dental Clinic. All rights reserved.</p>
                    <a asp-area="" asp-page="/Privacy" class="text-white text-decoration-none">
                        <i class="fas fa-shield-alt me-1"></i>Privacy Policy
                    </a>
                </div>
            </div>
        </div>
    </footer>

    <!-- MDB5 JS -->
    <script src="~/lib/MDB5-STANDARD-UI-KIT/js/mdb.umd.min.js"></script>
    <!-- Custom JS -->
    <script src="~/js/site.js" asp-append-version="true"></script>

    <!-- Initialize MDB5 Components -->
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Initialize ripple effects for all buttons
            const buttons = document.querySelectorAll('.btn');
            buttons.forEach((button) => {
                new mdb.Ripple(button);
            });

            // Initialize navbar collapse
            const navbarToggler = document.querySelector('.navbar-toggler');
            const navbarCollapse = document.querySelector('.navbar-collapse');

            if (navbarToggler && navbarCollapse) {
                navbarToggler.addEventListener('click', function() {
                    navbarCollapse.classList.toggle('show');
                });

                // Close navbar when clicking on nav links (mobile)
                const navLinks = document.querySelectorAll('.nav-link');
                navLinks.forEach(link => {
                    link.addEventListener('click', function() {
                        if (window.innerWidth < 1200) { // XL breakpoint
                            navbarCollapse.classList.remove('show');
                        }
                    });
                });
            }

            // Initialize animations
            const animatedElements = document.querySelectorAll('[data-mdb-animation-init]');
            animatedElements.forEach((element) => {
                new mdb.Animate(element);
            });
        });
    </script>

    @await RenderSectionAsync("Scripts", required: false)
</body>
</html>
