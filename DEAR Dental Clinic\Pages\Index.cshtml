﻿@page
@model IndexModel
@{
    ViewData["Title"] = "Home - DEAR Dental Clinic";
}

<!-- MDB5 Responsive Hero Section -->
<section class="hero-section bg-gradient-primary">
    <div class="container">
        <div class="row align-items-center min-vh-100">
            <div class="col-xl-6 col-lg-6 col-md-12 col-sm-12 order-2 order-lg-1" data-mdb-animation-init data-mdb-animation="fade-in-left">
                <div class="hero-content text-white text-center text-lg-start">
                    <h1 class="display-4 fw-bold mb-4">
                        Welcome to <span class="text-warning">DEAR Dental Clinic</span>
                    </h1>
                    <p class="lead mb-4">
                        Your trusted partner for comprehensive dental care with modern technology and compassionate service.
                    </p>
                    <div class="hero-buttons d-flex flex-column flex-sm-row justify-content-center justify-content-lg-start">
                        <a href="/BookAppointment" class="btn btn-warning btn-lg rounded-pill me-sm-3 mb-3 mb-sm-0 shadow-3">
                            <i class="fas fa-calendar-plus me-2"></i>Book Appointment
                        </a>
                        <a href="/Services" class="btn btn-outline-light btn-lg rounded-pill shadow-2">
                            <i class="fas fa-tooth me-2"></i>Our Services
                        </a>
                    </div>
                </div>
            </div>
            <div class="col-xl-6 col-lg-6 col-md-12 col-sm-12 order-1 order-lg-2 mb-4 mb-lg-0" data-mdb-animation-init data-mdb-animation="fade-in-right">
                <div class="hero-image text-center">
                    <div class="image-placeholder bg-white bg-opacity-10 rounded-6 p-4 p-md-5 shadow-4">
                        <i class="fas fa-tooth fa-4x fa-md-5x text-warning mb-3"></i>
                        <h4 class="text-white fw-bold mb-2">Professional Dental Care</h4>
                        <p class="text-white-50 mb-0">Modern technology meets compassionate care</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

@section Scripts {
    <script>
        // Initialize MDB5 components and ripple effects
        document.addEventListener('DOMContentLoaded', function() {
            // Initialize ripple effects for all buttons
            const buttons = document.querySelectorAll('.btn');
            buttons.forEach((button) => {
                new mdb.Ripple(button);
            });
        });
    </script>
}
