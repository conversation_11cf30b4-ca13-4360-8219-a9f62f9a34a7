@page
@model DEAR_Dental_Clinic.Pages.ServicesModel
@{
    ViewData["Title"] = "Services - DEAR Dental Clinic";
}

<!-- MDB5 Services Section -->
<div class="container py-5">
    <div class="row">
        <div class="col-12 text-center mb-5" data-mdb-animation-init data-mdb-animation="fade-in-up">
            <h1 class="display-5 fw-bold text-primary mb-3">Our Services</h1>
            <p class="lead text-muted">Comprehensive dental care for the whole family</p>
        </div>
    </div>

    <div class="row">
        <div class="col-xl-4 col-lg-6 col-md-6 col-sm-12 mb-4" data-mdb-animation-init data-mdb-animation="fade-in-up" data-mdb-animation-delay="100">
            <div class="card h-100 shadow-3 hover-shadow">
                <div class="card-body text-center">
                    <div class="service-icon mb-3">
                        <i class="fas fa-tooth fa-3x text-primary"></i>
                    </div>
                    <h4 class="card-title fw-bold mb-3">General Dentistry</h4>
                    <p class="card-text text-muted mb-3">Regular checkups, cleanings, and preventive care to maintain your oral health.</p>
                    <ul class="list-unstyled text-start">
                        <li class="mb-2"><i class="fas fa-check text-success me-2"></i>Dental Cleanings</li>
                        <li class="mb-2"><i class="fas fa-check text-success me-2"></i>Oral Examinations</li>
                        <li class="mb-2"><i class="fas fa-check text-success me-2"></i>Fluoride Treatments</li>
                        <li class="mb-2"><i class="fas fa-check text-success me-2"></i>Dental Sealants</li>
                    </ul>
                </div>
            </div>
        </div>

        <div class="col-xl-4 col-lg-6 col-md-6 col-sm-12 mb-4" data-mdb-animation-init data-mdb-animation="fade-in-up" data-mdb-animation-delay="200">
            <div class="card h-100 shadow-3 hover-shadow">
                <div class="card-body text-center">
                    <div class="service-icon mb-3">
                        <i class="fas fa-smile fa-3x text-warning"></i>
                    </div>
                    <h4 class="card-title fw-bold mb-3">Cosmetic Dentistry</h4>
                    <p class="card-text text-muted mb-3">Enhance your smile with whitening, veneers, and aesthetic treatments.</p>
                    <ul class="list-unstyled text-start">
                        <li class="mb-2"><i class="fas fa-check text-success me-2"></i>Teeth Whitening</li>
                        <li class="mb-2"><i class="fas fa-check text-success me-2"></i>Dental Veneers</li>
                        <li class="mb-2"><i class="fas fa-check text-success me-2"></i>Dental Bonding</li>
                        <li class="mb-2"><i class="fas fa-check text-success me-2"></i>Smile Makeovers</li>
                    </ul>
                </div>
            </div>
        </div>

        <div class="col-xl-4 col-lg-6 col-md-6 col-sm-12 mb-4" data-mdb-animation-init data-mdb-animation="fade-in-up" data-mdb-animation-delay="300">
            <div class="card h-100 shadow-3 hover-shadow">
                <div class="card-body text-center">
                    <div class="service-icon mb-3">
                        <i class="fas fa-user-md fa-3x text-info"></i>
                    </div>
                    <h4 class="card-title fw-bold mb-3">Oral Surgery</h4>
                    <p class="card-text text-muted mb-3">Expert surgical procedures including extractions and implant placement.</p>
                    <ul class="list-unstyled text-start">
                        <li class="mb-2"><i class="fas fa-check text-success me-2"></i>Tooth Extractions</li>
                        <li class="mb-2"><i class="fas fa-check text-success me-2"></i>Dental Implants</li>
                        <li class="mb-2"><i class="fas fa-check text-success me-2"></i>Wisdom Teeth Removal</li>
                        <li class="mb-2"><i class="fas fa-check text-success me-2"></i>Oral Biopsies</li>
                    </ul>
                </div>
            </div>
        </div>

        <div class="col-xl-4 col-lg-6 col-md-6 col-sm-12 mb-4" data-mdb-animation-init data-mdb-animation="fade-in-up" data-mdb-animation-delay="400">
            <div class="card h-100 shadow-3 hover-shadow">
                <div class="card-body text-center">
                    <div class="service-icon mb-3">
                        <i class="fas fa-shield-alt fa-3x text-success"></i>
                    </div>
                    <h4 class="card-title fw-bold mb-3">Preventive Care</h4>
                    <p class="card-text text-muted mb-3">Protect your teeth and gums with our comprehensive preventive treatments.</p>
                    <ul class="list-unstyled text-start">
                        <li class="mb-2"><i class="fas fa-check text-success me-2"></i>Regular Checkups</li>
                        <li class="mb-2"><i class="fas fa-check text-success me-2"></i>Gum Disease Treatment</li>
                        <li class="mb-2"><i class="fas fa-check text-success me-2"></i>Oral Cancer Screening</li>
                        <li class="mb-2"><i class="fas fa-check text-success me-2"></i>Custom Mouthguards</li>
                    </ul>
                </div>
            </div>
        </div>

        <div class="col-xl-4 col-lg-6 col-md-6 col-sm-12 mb-4" data-mdb-animation-init data-mdb-animation="fade-in-up" data-mdb-animation-delay="500">
            <div class="card h-100 shadow-3 hover-shadow">
                <div class="card-body text-center">
                    <div class="service-icon mb-3">
                        <i class="fas fa-tools fa-3x text-danger"></i>
                    </div>
                    <h4 class="card-title fw-bold mb-3">Restorative Dentistry</h4>
                    <p class="card-text text-muted mb-3">Restore damaged teeth with crowns, bridges, and fillings.</p>
                    <ul class="list-unstyled text-start">
                        <li class="mb-2"><i class="fas fa-check text-success me-2"></i>Dental Crowns</li>
                        <li class="mb-2"><i class="fas fa-check text-success me-2"></i>Dental Bridges</li>
                        <li class="mb-2"><i class="fas fa-check text-success me-2"></i>Dental Fillings</li>
                        <li class="mb-2"><i class="fas fa-check text-success me-2"></i>Root Canal Therapy</li>
                    </ul>
                </div>
            </div>
        </div>

        <div class="col-xl-4 col-lg-6 col-md-6 col-sm-12 mb-4" data-mdb-animation-init data-mdb-animation="fade-in-up" data-mdb-animation-delay="600">
            <div class="card h-100 shadow-3 hover-shadow">
                <div class="card-body text-center">
                    <div class="service-icon mb-3">
                        <i class="fas fa-child fa-3x text-secondary"></i>
                    </div>
                    <h4 class="card-title fw-bold mb-3">Pediatric Dentistry</h4>
                    <p class="card-text text-muted mb-3">Specialized dental care for children in a friendly environment.</p>
                    <ul class="list-unstyled text-start">
                        <li class="mb-2"><i class="fas fa-check text-success me-2"></i>Children's Cleanings</li>
                        <li class="mb-2"><i class="fas fa-check text-success me-2"></i>Fluoride Applications</li>
                        <li class="mb-2"><i class="fas fa-check text-success me-2"></i>Dental Education</li>
                        <li class="mb-2"><i class="fas fa-check text-success me-2"></i>Early Orthodontic Care</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>

    <div class="row mt-4">
        <div class="col-12 text-center" data-mdb-animation-init data-mdb-animation="fade-in-up">
            <div class="card bg-gradient-primary text-white shadow-5">
                <div class="card-body p-5">
                    <h3 class="fw-bold mb-3">Ready to Schedule Your Appointment?</h3>
                    <p class="lead mb-4">Contact us today to book your visit with our experienced dental team.</p>
                    <a href="/Contact" class="btn btn-warning btn-lg rounded-pill me-3 shadow-3">
                        <i class="fas fa-phone me-2"></i>Contact Us
                    </a>
                    <a href="/BookAppointment" class="btn btn-outline-light btn-lg rounded-pill shadow-2">
                        <i class="fas fa-calendar-plus me-2"></i>Book Online
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    <script>
        // Initialize MDB5 components and ripple effects
        document.addEventListener('DOMContentLoaded', function() {
            // Initialize ripple effects for all buttons
            const buttons = document.querySelectorAll('.btn');
            buttons.forEach((button) => {
                new mdb.Ripple(button);
            });
        });
    </script>
}
