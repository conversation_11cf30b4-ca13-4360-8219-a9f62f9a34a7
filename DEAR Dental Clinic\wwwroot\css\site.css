/* MDB5 Custom Styles */
html {
  font-size: 14px;
  position: relative;
  min-height: 100%;
}

body {
  margin: 0;
  font-family: '<PERSON><PERSON>', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  line-height: 1.6;
  display: flex;
  flex-direction: column;
  min-height: 100vh;
}

main {
  flex: 1;
}

/* MDB5 Gradient Backgrounds */
.bg-gradient-primary {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

/* Custom hover effects for MDB5 cards */
.hover-shadow {
  transition: all 0.3s ease;
}

.hover-shadow:hover {
  transform: translateY(-5px);
}

/* MDB5 Hero Section */
.hero-section {
  min-height: 80vh;
  display: flex;
  align-items: center;
  padding: 60px 0;
}

.hero-content {
  animation: fadeInLeft 1s ease-out;
}

.hero-image {
  text-align: center;
  animation: fadeInRight 1s ease-out;
}

.hero-buttons .btn {
  margin-bottom: 1rem;
  transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1) !important;
  position: relative;
  overflow: hidden;
}

.hero-buttons .btn:hover {
  transform: translateY(-4px) !important;
  box-shadow: 0 12px 30px rgba(0, 0, 0, 0.3) !important;
}

.hero-buttons .btn:active {
  transform: translateY(-2px) !important;
}

/* MDB5 Animation keyframes */
@keyframes fadeInLeft {
  from {
    opacity: 0;
    transform: translateX(-50px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes fadeInRight {
  from {
    opacity: 0;
    transform: translateX(50px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

/* MDB5 Utility Classes */
.min-vh-100 {
  min-height: 100vh;
}

/* MDB5 Custom Card Styles */
.card {
  border: none;
  transition: all 0.3s ease;
}

.card:hover {
  transform: translateY(-5px);
}

/* MDB5 Form Enhancements */
.form-outline {
  position: relative;
}

.form-outline .form-control {
  border-radius: 0.5rem;
}

/* MDB5 Page-specific Styles */
.login-page {
  min-height: 100vh;
}

.about-features {
  list-style: none;
  padding: 0;
}

.about-features li {
  padding: 0.5rem 0;
  font-size: 1.1rem;
}

.about-features i {
  margin-right: 0.5rem;
}

.contact-icon {
  font-size: 2.5rem;
  margin-bottom: 1rem;
}

.hours-item {
  display: flex;
  justify-content: space-between;
  padding: 0.5rem 0;
  border-bottom: 1px solid #eee;
}

.hours-item:last-child {
  border-bottom: none;
}

.day {
  font-weight: 600;
}

.time {
  color: #666;
}

/* MDB5 Navigation Responsive */
.navbar-collapse {
  align-items: center;
  transition: all 0.3s ease;
}

.navbar-nav {
  margin: 0 auto;
}

/* Navbar Collapse Animation */
@media (max-width: 1199px) {
  .navbar-collapse {
    overflow: hidden;
    max-height: 0;
    transition: max-height 0.3s ease;
  }

  .navbar-collapse.show {
    max-height: 500px;
  }

  .navbar-collapse.collapsing {
    max-height: 0;
    transition: max-height 0.3s ease;
  }
}

/* Desktop navbar - always show */
@media (min-width: 1200px) {
  .navbar-collapse {
    display: flex !important;
    max-height: none !important;
    overflow: visible !important;
  }
}

/* Navbar Brand Hover Effect */
.navbar-brand {
  transition: all 0.3s ease !important;
  padding: 8px 12px !important;
  border-radius: 8px !important;
}

.navbar-brand:hover {
  background: rgba(25, 118, 210, 0.05) !important;
  color: #1976d2 !important;
  transform: scale(1.05) !important;
  text-decoration: none !important;
}

.navbar-brand i {
  transition: all 0.3s ease !important;
}

.navbar-brand:hover i {
  transform: rotate(10deg) !important;
  color: #1976d2 !important;
}

/* Active Navigation States */
.nav-link.active {
  position: relative;
  font-weight: 700 !important;
}

.nav-link.active::after {
  content: '';
  position: absolute;
  bottom: -5px;
  left: 50%;
  transform: translateX(-50%);
  width: 30px;
  height: 3px;
  background: var(--mdb-primary);
  border-radius: 2px;
}

/* Enhanced Button Styles with Hover Effects */
.btn {
  position: relative;
  overflow: hidden;
  transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1) !important;
  border: none !important;
}

.btn:hover {
  transform: translateY(-3px) !important;
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2) !important;
}

.btn:active {
  transform: translateY(-1px) !important;
  transition: all 0.1s ease !important;
}

/* Primary Button Enhancements */
.btn-primary {
  background: linear-gradient(45deg, #1976d2, #42a5f5) !important;
  color: white !important;
}

.btn-primary:hover {
  background: linear-gradient(45deg, #1565c0, #1976d2) !important;
  color: white !important;
}

/* Warning Button Enhancements */
.btn-warning {
  background: linear-gradient(45deg, #f57c00, #ffb74d) !important;
  color: white !important;
}

.btn-warning:hover {
  background: linear-gradient(45deg, #ef6c00, #f57c00) !important;
  color: white !important;
}

/* Outline Button Enhancements */
.btn-outline-primary {
  border: 2px solid #1976d2 !important;
  color: #1976d2 !important;
  background: transparent !important;
}

.btn-outline-primary:hover {
  background: #1976d2 !important;
  color: white !important;
  border-color: #1976d2 !important;
}

.btn-outline-light {
  border: 2px solid rgba(255, 255, 255, 0.7) !important;
  color: white !important;
  background: transparent !important;
}

.btn-outline-light:hover {
  background: rgba(255, 255, 255, 0.2) !important;
  border-color: white !important;
  color: white !important;
}

.btn-outline-secondary {
  border: 2px solid #6c757d !important;
  color: #6c757d !important;
  background: transparent !important;
}

.btn-outline-secondary:hover {
  background: #6c757d !important;
  color: white !important;
  border-color: #6c757d !important;
}

/* Responsive Button Sizes */
.btn {
  font-weight: 600 !important;
  transition: all 0.3s ease !important;
}

.btn-sm {
  padding: 6px 12px !important;
  font-size: 0.875rem !important;
}

.btn-md, .btn {
  padding: 8px 16px !important;
  font-size: 1rem !important;
}

.btn-lg {
  padding: 10px 20px !important;
  font-size: 1.1rem !important;
}

/* Rounded Pill Buttons */
.rounded-pill {
  border-radius: 50px !important;
}

/* Force hover effects to work */
a.btn:hover,
button.btn:hover,
input[type="submit"].btn:hover {
  transform: translateY(-3px) !important;
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2) !important;
  text-decoration: none !important;
}

/* Navigation Links Hover Effects */
.navbar-nav .nav-link {
  transition: all 0.3s ease !important;
  position: relative;
  padding: 8px 16px !important;
  border-radius: 8px !important;
}

.navbar-nav .nav-link:hover {
  background: rgba(25, 118, 210, 0.1) !important;
  color: #1976d2 !important;
  transform: translateY(-1px) !important;
}

.navbar-nav .nav-link.active:hover {
  background: rgba(25, 118, 210, 0.15) !important;
}

/* Navigation button specific hover */
.navbar .btn:hover {
  transform: translateY(-2px) !important;
  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.15) !important;
}

/* Login button in navbar specific styling */
.navbar .btn-outline-primary:hover {
  background: #1976d2 !important;
  color: white !important;
  border-color: #1976d2 !important;
  transform: translateY(-2px) !important;
  box-shadow: 0 6px 20px rgba(25, 118, 210, 0.3) !important;
}

/* Card hover effects enhancement */
.card:hover {
  transform: translateY(-5px) !important;
  box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1) !important;
}

/* Ensure transitions work on all elements */
* {
  -webkit-transition: all 0.3s ease;
  -moz-transition: all 0.3s ease;
  -o-transition: all 0.3s ease;
  transition: all 0.3s ease;
}

/* Hover effects for cards */
.hover-shadow {
  transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
}

.hover-shadow:hover {
  transform: translateY(-8px);
  box-shadow: 0 12px 20px rgba(0, 0, 0, 0.15) !important;
}

/* Utility Classes */
.min-vh-100 {
  min-height: 100vh;
}

/* Responsive Design - Media Queries */

/* Extra Small Devices (max-width: 575px) */
@media (max-width: 575px) {
  .container {
    padding-left: 15px;
    padding-right: 15px;
  }

  /* Navigation */
  .navbar-brand {
    font-size: 1rem !important;
  }

  .navbar-nav {
    text-align: center;
    width: 100%;
  }

  .navbar-nav .nav-link {
    padding: 12px 20px !important;
    margin: 5px 0 !important;
    border-radius: 8px !important;
  }

  .navbar .btn {
    width: 100% !important;
    margin-top: 15px !important;
    padding: 12px !important;
  }

  /* Hero Section */
  .hero-section {
    padding: 30px 0 !important;
    min-height: 60vh !important;
  }

  .display-4 {
    font-size: 1.8rem !important;
  }

  .lead {
    font-size: 1rem !important;
  }

  .hero-buttons .btn {
    display: block;
    width: 100%;
    margin-bottom: 15px !important;
    padding: 12px 16px !important;
    font-size: 0.9rem !important;
  }

  /* Button responsive sizing */
  .btn-lg {
    padding: 10px 16px !important;
    font-size: 0.95rem !important;
  }

  .btn {
    padding: 8px 12px !important;
    font-size: 0.875rem !important;
  }

  /* Cards */
  .card {
    margin-bottom: 20px !important;
  }

  .card-body {
    padding: 20px !important;
  }

  /* Forms */
  .form-control-lg {
    font-size: 1rem !important;
    padding: 12px !important;
  }

  /* Typography */
  .display-5 {
    font-size: 1.6rem !important;
  }

  h1 {
    font-size: 1.8rem !important;
  }

  h2 {
    font-size: 1.5rem !important;
  }

  h3 {
    font-size: 1.3rem !important;
  }

  /* Spacing */
  .py-5 {
    padding-top: 2rem !important;
    padding-bottom: 2rem !important;
  }

  .mb-5 {
    margin-bottom: 2rem !important;
  }

  /* Disable hover transforms on mobile */
  .btn:hover,
  .card:hover,
  .navbar-brand:hover {
    transform: none !important;
  }
}

/* Small Devices (min-width: 576px and max-width: 767px) */
@media (min-width: 576px) and (max-width: 767px) {
  .container {
    max-width: 540px;
  }

  /* Navigation */
  .navbar-brand {
    font-size: 1.1rem !important;
  }

  .navbar-nav {
    text-align: center;
    width: 100%;
  }

  .navbar-nav .nav-link {
    padding: 10px 16px !important;
    margin: 3px 0 !important;
  }

  .navbar .btn {
    margin-top: 10px !important;
    padding: 10px 20px !important;
  }

  /* Hero Section */
  .hero-section {
    padding: 40px 0 !important;
    min-height: 65vh !important;
  }

  .display-4 {
    font-size: 2.2rem !important;
  }

  .lead {
    font-size: 1.1rem !important;
  }

  .hero-buttons .btn {
    display: inline-block;
    width: auto;
    margin: 8px !important;
    padding: 10px 20px !important;
    font-size: 0.95rem !important;
  }

  /* Button responsive sizing */
  .btn-lg {
    padding: 12px 20px !important;
    font-size: 1rem !important;
  }

  .btn {
    padding: 8px 16px !important;
    font-size: 0.9rem !important;
  }

  /* Cards */
  .card-body {
    padding: 25px !important;
  }

  /* Forms */
  .form-control-lg {
    font-size: 1.1rem !important;
    padding: 14px !important;
  }

  /* Typography */
  .display-5 {
    font-size: 1.8rem !important;
  }

  h1 {
    font-size: 2rem !important;
  }

  h2 {
    font-size: 1.7rem !important;
  }

  /* Spacing */
  .py-5 {
    padding-top: 3rem !important;
    padding-bottom: 3rem !important;
  }
}

/* Medium Devices (min-width: 768px and max-width: 991px) */
@media (min-width: 768px) and (max-width: 991px) {
  .container {
    max-width: 720px;
  }

  /* Navigation */
  .navbar-brand {
    font-size: 1.2rem !important;
  }

  .navbar-nav {
    margin: 0 auto;
  }

  .navbar-nav .nav-link {
    padding: 8px 12px !important;
    margin: 0 5px !important;
  }

  .navbar .btn {
    padding: 8px 16px !important;
  }

  /* Hero Section */
  .hero-section {
    padding: 50px 0 !important;
    min-height: 70vh !important;
  }

  .display-4 {
    font-size: 2.5rem !important;
  }

  .lead {
    font-size: 1.15rem !important;
  }

  .hero-buttons .btn {
    margin: 10px !important;
    padding: 12px 24px !important;
    font-size: 1rem !important;
  }

  /* Button responsive sizing */
  .btn-lg {
    padding: 14px 24px !important;
    font-size: 1.05rem !important;
  }

  .btn {
    padding: 10px 18px !important;
    font-size: 0.95rem !important;
  }

  /* Cards */
  .card-body {
    padding: 30px !important;
  }

  /* Forms */
  .form-control-lg {
    font-size: 1.15rem !important;
    padding: 16px !important;
  }

  /* Typography */
  .display-5 {
    font-size: 2rem !important;
  }

  h1 {
    font-size: 2.2rem !important;
  }

  h2 {
    font-size: 1.8rem !important;
  }
}

/* Large Devices (min-width: 992px and max-width: 1199px) */
@media (min-width: 992px) and (max-width: 1199px) {
  .container {
    max-width: 960px;
  }

  /* Navigation */
  .navbar-brand {
    font-size: 1.3rem !important;
  }

  .navbar-nav .nav-link {
    padding: 8px 15px !important;
    margin: 0 8px !important;
  }

  .navbar .btn {
    padding: 10px 20px !important;
  }

  /* Hero Section */
  .hero-section {
    padding: 60px 0 !important;
    min-height: 75vh !important;
  }

  .display-4 {
    font-size: 3rem !important;
  }

  .lead {
    font-size: 1.2rem !important;
  }

  .hero-buttons .btn {
    margin: 12px !important;
    padding: 14px 28px !important;
    font-size: 1.05rem !important;
  }

  /* Button responsive sizing */
  .btn-lg {
    padding: 16px 28px !important;
    font-size: 1.1rem !important;
  }

  .btn {
    padding: 12px 20px !important;
    font-size: 1rem !important;
  }

  /* Cards */
  .card-body {
    padding: 35px !important;
  }

  /* Forms */
  .form-control-lg {
    font-size: 1.2rem !important;
    padding: 18px !important;
  }

  /* Typography */
  .display-5 {
    font-size: 2.3rem !important;
  }

  h1 {
    font-size: 2.5rem !important;
  }

  h2 {
    font-size: 2rem !important;
  }
}

/* Extra Large Devices (min-width: 1200px) */
@media (min-width: 1200px) {
  .container {
    max-width: 1140px;
  }

  /* Navigation */
  .navbar-brand {
    font-size: 1.4rem !important;
  }

  .navbar-nav .nav-link {
    padding: 10px 18px !important;
    margin: 0 10px !important;
  }

  .navbar .btn {
    padding: 12px 24px !important;
  }

  /* Hero Section */
  .hero-section {
    padding: 70px 0 !important;
    min-height: 80vh !important;
  }

  .display-4 {
    font-size: 3.5rem !important;
  }

  .lead {
    font-size: 1.3rem !important;
  }

  .hero-buttons .btn {
    margin: 15px !important;
    padding: 16px 32px !important;
    font-size: 1.1rem !important;
  }

  /* Button responsive sizing */
  .btn-lg {
    padding: 18px 32px !important;
    font-size: 1.15rem !important;
  }

  .btn {
    padding: 14px 24px !important;
    font-size: 1.05rem !important;
  }

  /* Cards */
  .card-body {
    padding: 40px !important;
  }

  /* Forms */
  .form-control-lg {
    font-size: 1.25rem !important;
    padding: 20px !important;
  }

  /* Typography */
  .display-5 {
    font-size: 2.5rem !important;
  }

  h1 {
    font-size: 2.8rem !important;
  }

  h2 {
    font-size: 2.2rem !important;
  }
}